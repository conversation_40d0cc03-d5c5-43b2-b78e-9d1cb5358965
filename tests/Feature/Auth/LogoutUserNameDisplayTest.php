<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(User::class)]
class LogoutUserNameDisplayTest extends TestCase
{
    use RefreshDatabase;

    protected User $clientUser;

    protected User $member;

    protected User $adminUser;

    protected function setUp(): void
    {
        parent::setUp();

        $this->clientUser = User::factory()->create([
            'name' => 'John Client',
            'email' => '<EMAIL>',
            'role' => 'user',
        ]);

        $this->member = User::factory()->create([
            'name' => 'Jane Member',
            'email' => '<EMAIL>',
            'role' => 'member',
        ]);

        $this->adminUser = User::factory()->create([
            'name' => 'Bob Admin',
            'email' => '<EMAIL>',
            'role' => 'admin',
        ]);
    }

    #[Test]
    public function client_user_name_displays_correctly_before_logout()
    {
        // Login as client
        $this->actingAs($this->clientUser);

        // Access dashboard - should show actual user name, not "Guest"
        $response = $this->get(route('user.dashboard'));
        $response->assertStatus(200);

        // Should see actual user name in header and dashboard
        $response->assertSee('John Client');
        $response->assertSee('Welcome, John Client!');

        // Should NOT see fallback values
        $response->assertDontSee('Welcome, Guest!');
    }

    #[Test]
    public function normal_user_name_displays_correctly_before_logout()
    {
        // Login as member
        $this->actingAs($this->member);

        // Access dashboard - should show actual user name, not "Guest"
        $response = $this->get(route('member.dashboard'));
        $response->assertStatus(200);

        // Should see actual user name in header and dashboard
        $response->assertSee('Jane Member');
        $response->assertSee('Welcome, Jane Member!');

        // Should NOT see fallback values
        $response->assertDontSee('Welcome, Guest!');
    }

    #[Test]
    public function admin_user_name_displays_correctly_before_logout()
    {
        // Login as admin
        $this->actingAs($this->adminUser);

        // Access dashboard - should show actual user name, not "Admin"
        $response = $this->get(route('admin.dashboard'));
        $response->assertStatus(200);

        // Should see actual user name in header and dashboard
        $response->assertSee('Bob Admin');
        $response->assertSee('Welcome, Bob Admin!');

        // Should NOT see fallback values
        $response->assertDontSee('Welcome, Admin!');
    }

    #[Test]
    public function header_shows_actual_user_name_not_guest_for_all_user_types()
    {
        // Test client user
        $this->actingAs($this->clientUser);
        $response = $this->get(route('user.dashboard'));
        $response->assertStatus(200);
        $response->assertSee('John Client');
        $response->assertDontSee('Guest');

        // Test member
        $this->actingAs($this->member);
        $response = $this->get(route('member.dashboard'));
        $response->assertStatus(200);
        $response->assertSee('Jane Member');
        $response->assertDontSee('Guest');

        // Test admin user
        $this->actingAs($this->adminUser);
        $response = $this->get(route('admin.dashboard'));
        $response->assertStatus(200);
        $response->assertSee('Bob Admin');
        $response->assertDontSee('Guest');
    }

    #[Test]
    public function user_account_information_shows_actual_data_not_fallbacks()
    {
        // Test client user account information - only client dashboard has account info section
        $this->actingAs($this->clientUser);
        $response = $this->get(route('user.dashboard'));
        $response->assertStatus(200);

        // Should see actual email in account information section, not fallback
        $response->assertSee('<EMAIL>');
        $response->assertDontSee('N/A');
        $response->assertSee('Your Account Information');

        // Test member dashboard - member dashboard doesn't have account information section
        $this->actingAs($this->member);
        $response = $this->get(route('member.dashboard'));
        $response->assertStatus(200);

        // Member dashboard should show user name but doesn't have account info section
        $response->assertSee('Jane Member');
        $response->assertDontSee('Your Account Information');
        // Member dashboard focuses on reservations, not account details
        $response->assertSee('Upcoming reservations this week');
    }

    #[Test]
    public function logout_process_completes_successfully_with_proper_user_name_display()
    {
        // Login as client
        $this->actingAs($this->clientUser);

        // Verify user name is displayed correctly before logout
        $response = $this->get(route('user.dashboard'));
        $response->assertStatus(200);
        $response->assertSee('John Client');

        // Perform logout - should complete successfully
        $response = $this->post(route('logout'));
        $response->assertStatus(302);
        $response->assertRedirect('/');

        // Verify user is logged out
        $this->assertGuest();
    }

    #[Test]
    public function role_based_visibility_still_works_with_corrected_user_name_display()
    {
        // Test client user - should not see reservation features
        $this->actingAs($this->clientUser);
        $response = $this->get(route('user.dashboard'));
        $response->assertStatus(200);

        // Should see user name but not reservation features
        $response->assertSee('John Client');
        $response->assertDontSee('New Reservation');
        $response->assertDontSee('Reservations');

        // Test member - should see reservation features
        $this->actingAs($this->member);
        $response = $this->get(route('member.dashboard'));
        $response->assertStatus(200);

        // Should see user name and reservation features
        $response->assertSee('Jane Member');
        $response->assertSee('New Reservation');
        $response->assertSee('Reservations');
    }

    #[Test]
    public function null_safe_operators_still_work_for_method_calls()
    {
        // Login as member
        $this->actingAs($this->member);

        // Access dashboard - role-based visibility should work
        $response = $this->get(route('member.dashboard'));
        $response->assertStatus(200);

        // Should see reservation features (method calls work)
        $response->assertSee('New Reservation');

        // Logout should work without null reference errors
        $response = $this->post(route('logout'));
        $response->assertStatus(302);
        $this->assertGuest();
    }

    #[Test]
    public function fallback_values_only_show_when_user_is_actually_null()
    {
        // Access dashboard without authentication
        $response = $this->get(route('user.dashboard'));

        // Should redirect to login, not show fallback values
        $response->assertStatus(302);
        $response->assertRedirect(route('login'));
    }

    #[Test]
    public function user_properties_vs_method_calls_handled_correctly()
    {
        // Login as client
        $this->actingAs($this->clientUser);

        $response = $this->get(route('user.dashboard'));
        $response->assertStatus(200);

        $content = $response->getContent();

        // User properties should show actual values
        $this->assertStringContainsString('John Client', $content);
        $this->assertStringContainsString('<EMAIL>', $content);

        // Role-based method calls should work (client shouldn't see FPMP features)
        $this->assertStringNotContainsString('Quick Reserve', $content);
        $this->assertStringNotContainsString('New Reservation', $content);
    }

    #[Test]
    public function all_user_types_can_logout_with_correct_name_display()
    {
        // Test client logout
        $this->actingAs($this->clientUser);
        $response = $this->get(route('user.dashboard'));
        $response->assertSee('John Client');

        $response = $this->post(route('logout'));
        $response->assertStatus(302);
        $this->assertGuest();

        // Test member logout
        $this->actingAs($this->member);
        $response = $this->get(route('member.dashboard'));
        $response->assertSee('Jane Member');

        $response = $this->post(route('logout'));
        $response->assertStatus(302);
        $this->assertGuest();

        // Test admin logout
        $this->actingAs($this->adminUser);
        $response = $this->get(route('admin.dashboard'));
        $response->assertSee('Bob Admin');

        $response = $this->post(route('logout'));
        $response->assertStatus(302);
        $this->assertGuest();
    }

    #[Test]
    public function header_template_shows_correct_user_info_for_all_roles()
    {
        // Test client in header
        $this->actingAs($this->clientUser);
        $response = $this->get(route('user.dashboard'));
        $response->assertStatus(200);

        $content = $response->getContent();
        $this->assertStringContainsString('John Client', $content);
        $this->assertStringContainsString('Client', $content); // Role display

        // Test member in header
        $this->actingAs($this->member);
        $response = $this->get(route('member.dashboard'));
        $response->assertStatus(200);

        $content = $response->getContent();
        $this->assertStringContainsString('Jane Member', $content);
        $this->assertStringContainsString('Member', $content); // Role display

        // Test admin in header
        $this->actingAs($this->adminUser);
        $response = $this->get(route('admin.dashboard'));
        $response->assertStatus(200);

        $content = $response->getContent();
        $this->assertStringContainsString('Bob Admin', $content);
        $this->assertStringContainsString('Admin', $content); // Role display
    }
}
