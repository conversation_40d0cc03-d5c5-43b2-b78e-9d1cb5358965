@extends('layouts.admin')

@section('title', 'Reservation Details - SMP Online')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Reservation Management</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('reservations.index') }}">Reservations</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Reservation #{{ $reservation->id }}</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- <PERSON> Header Close -->

    <!-- Success/Error Messages -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="ti ti-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="ti ti-exclamation-triangle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Reservation Details -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        <i class="ti ti-calendar-event me-2"></i>Reservation #{{ $reservation->id }} Details
                        <span
                            class="badge bg-{{ $reservation->status_color }}-transparent text-{{ $reservation->status_color }} ms-2">
                            {{ $reservation->status }}
                        </span>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('reservations.index') }}" class="btn btn-primary btn-sm">
                            <i class="ti ti-list me-1"></i>View List
                        </a>
                        <a href="{{ route('calendar.index') }}" class="btn btn-success btn-sm">
                            <i class="ti ti-calendar me-1"></i>View Calendar
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row gy-4">

                        <!-- Reservation Schedule -->
                        <div class="col-xl-5">
                            <div class="card custom-card shadow-none border">
                                <div class="card-header">
                                    <div class="card-title">Schedule</div>
                                </div>
                                <div class="card-body pb-0">
                                    <div class="col-auto">
                                        <div class="d-flex align-items-center mb-3">
                                            <div class="me-3">
                                                <span class="avatar avatar-lg bg-dark-transparent">
                                                    <i class="{{ $reservation->field->icon ?? 'bx bx-stadium' }}" style="font-size: 1.5rem;"></i>
                                                </span>
                                            </div>
                                            <div>
                                                <small class="text-muted">Field</small>
                                                <h5 class="mb-1">{{ $reservation->field->name }}</h5>
                                            </div>
                                        </div>

                                        <div class="card-body">
                                            <div class="row gy-3">

                                                <div class="col-6">
                                                    <div class="d-flex align-items-center">
                                                        <i class="ti ti-calendar me-2 text-primary"></i>
                                                        <div>
                                                            <small class="text-muted">Date</small>
                                                            @if ($reservation->booking_date->isToday())
                                                                <small class="text-muted"> - </small><small class="text-primary">Today</small>
                                                            @elseif($reservation->booking_date->isTomorrow())
                                                                <small class="text-muted"> - </small><small class="text-primary">Tomorrow</small>
                                                            @endif
                                                            <div class="fw-semibold">
                                                                {{ $reservation->booking_date->format('l, F d, Y') }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-6">
                                                    <div class="d-flex align-items-center">
                                                        <i class="ti ti-clock me-2 text-info"></i>
                                                        <div>
                                                            <small class="text-muted">Time</small>
                                                            <div class="fw-semibold">{{ $reservation->time_range }}</div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-6">
                                                    <div class="d-flex align-items-center">
                                                        <i class="ti ti-user-plus me-2 text-success"></i>
                                                        <div>
                                                            <small class="text-muted">Reserved by member</small>
                                                            <div class="fw-semibold">{{ $reservation->user->name }}</div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-6">
                                                    <div class="d-flex align-items-center">
                                                        <i class="ti ti-hourglass me-2 text-warning"></i>
                                                        <div>
                                                            <small class="text-muted">Duration</small>
                                                            <div class="fw-semibold">{{ $reservation->duration_hours }}
                                                                {{ Str::plural('hour', $reservation->duration_hours) }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- utilities -->
                                                @if ($reservation->utilities && $reservation->utilities->count())
                                                <div class="col-6">
                                                    <div class="d-flex align-items-center">
                                                        <div>
                                                            <small class="text-muted" style="margin-left: 1.3rem;">Utilities</small>
                                                            @foreach ($reservation->utilities as $utility)
                                                                <div class="fw-semibold"><i class="{{ $utility->icon_class }} me-2 text-dark"></i>{{ $utility->name }} x {{ $utility->pivot->hours }}</div>
                                                            @endforeach
                                                        </div>
                                                    </div>
                                                </div>
                                                @endif
                                                <!-- end Utilities -->
                                                
                                                @if ($reservation->special_requests)
                                                <div class="col-12">
                                                    <div class="d-flex align-items-center">
                                                        <i class="bx bx-message-star me-2 text-dark"></i>
                                                        <div>
                                                            <small class="text-muted">Special Requests</small>
                                                            <div class="fw-semibold">{{ $reservation->special_requests }}</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>

                        <!-- Reservation Cost -->
                        <div class="col-xl-4">
                            <div class="card custom-card shadow-none border">
                                <div class="card-header">
                                    <div class="card-title">Cost Breakdown</div>
                                </div>
                                <div class="card-body">
                                    <div class="row gy-3">
                                        <!-- Cost Breakdown -->
                                        <div class="col-12">
                                            <div class="alert alert-success">
                                                @php
                                                    use App\Services\ReservationCostService;
                                                    $costService = new ReservationCostService();
                                                    $costBreakdown = $costService->getCostBreakdown(
                                                        $reservation->field,
                                                        $reservation->duration_hours,
                                                        $reservation->start_time
                                                    );
                                                    $utilityTotal = $reservation->utilities->sum('pivot.cost');
                                                @endphp

                                                <!-- Field Cost Breakdown -->
                                                <div class="mb-3">
                                                    <div class="fw-semibold mb-2">Field Cost:</div>
                                                    @if($costBreakdown['rate_breakdown'] && ($costBreakdown['rate_breakdown']['day_hours'] > 0 || $costBreakdown['rate_breakdown']['night_hours'] > 0))
                                                        <!-- Day/Night Rate Breakdown -->
                                                        @if($costBreakdown['rate_breakdown']['day_hours'] > 0)
                                                            <div class="fs-12 text-muted">
                                                                Day Rate: {{ $costBreakdown['rate_breakdown']['day_hours'] }} hours × XCG {{ number_format($reservation->field->hourly_rate, 2) }} = XCG {{ number_format($costBreakdown['rate_breakdown']['day_cost'], 2) }}
                                                            </div>
                                                        @endif
                                                        @if($costBreakdown['rate_breakdown']['night_hours'] > 0)
                                                            <div class="fs-12 text-muted">
                                                                Night Rate: {{ $costBreakdown['rate_breakdown']['night_hours'] }} hours × XCG {{ number_format($reservation->field->night_hourly_rate, 2) }} = XCG {{ number_format($costBreakdown['rate_breakdown']['night_cost'], 2) }}
                                                            </div>
                                                        @endif
                                                    @else
                                                        <!-- Simple Rate Display -->
                                                        <div class="fs-12 text-muted">
                                                            {{ $reservation->duration_hours }} hours × XCG {{ number_format($reservation->field->hourly_rate, 2) }} = XCG {{ number_format($costBreakdown['subtotal'], 2) }}
                                                        </div>
                                                    @endif
                                                    <div class="fs-13 fw-semibold mt-1">
                                                        Field Total: XCG {{ number_format($costBreakdown['subtotal'], 2) }}
                                                    </div>
                                                </div>

                                                <!-- Utility Cost Breakdown -->
                                                @if($reservation->utilities->count() > 0)
                                                    <div class="mb-3">
                                                        <div class="fw-semibold mb-2">Utility Costs:</div>
                                                        @foreach ($reservation->utilities as $utility)
                                                            <div class="fs-12 text-muted">
                                                                {{ $utility->name }}: {{ $utility->pivot->hours }} × XCG {{ number_format($utility->pivot->rate, 2) }} = XCG {{ number_format($utility->pivot->cost, 2) }}
                                                            </div>
                                                        @endforeach
                                                        <div class="fs-13 fw-semibold mt-1">
                                                            Utility Total: XCG {{ number_format($utilityTotal, 2) }}
                                                        </div>
                                                    </div>
                                                @endif

                                                <!-- Total Cost -->
                                                <div class="border-top pt-3">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <span class="fw-bold fs-16">Total Cost:</span>
                                                        </div>
                                                        <div class="h4 mb-0 text-success">
                                                            XCG {{ number_format($reservation->total_cost, 2) }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Reservation Status Timeline -->
                        <div class="col-xl-3">
                            <div class="card custom-card shadow-none border">
                                <div class="card-header">
                                    <div class="card-title">Status</div>
                                </div>
                                <div class="card-body">
                                    <div class="timeline">
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-primary"></div>
                                            <div class="timeline-content">
                                                <h6 class="timeline-title">Reservation Created</h6>
                                                <p class="timeline-text">{{ $reservation->created_at->format('M d, Y H:i') }}</p>
                                            </div>
                                        </div>

                                        @if ($reservation->confirmed_at)
                                            <div class="timeline-item">
                                                <div class="timeline-marker bg-success"></div>
                                                <div class="timeline-content">
                                                    <h6 class="timeline-title">Confirmed</h6>
                                                    <p class="timeline-text">{{ $reservation->confirmed_at->format('M d, Y H:i') }}</p>
                                                </div>
                                            </div>
                                        @endif

                                        @if ($reservation->cancelled_at)
                                            <div class="timeline-item">
                                                <div class="timeline-marker bg-danger"></div>
                                                <div class="timeline-content">
                                                    <h6 class="timeline-title">Cancelled</h6>
                                                    <p class="timeline-text">{{ $reservation->cancelled_at->format('M d, Y H:i') }}</p>
                                                </div>
                                            </div>
                                        @endif

                                        @if ($reservation->status === 'Confirmed' && $reservation->booking_date->isFuture())
                                            <div class="timeline-item">
                                                <div class="timeline-marker bg-info"></div>
                                                <div class="timeline-content">
                                                    <h6 class="timeline-title">Scheduled</h6>
                                                    <p class="timeline-text">{{ $reservation->formatted_date_time }}</p>
                                                </div>
                                            </div>
                                        @endif

                                        @if ($reservation->status === 'Completed')
                                            <div class="timeline-item">
                                                <div class="timeline-marker bg-dark"></div>
                                                <div class="timeline-content">
                                                    <h6 class="timeline-title">Completed</h6>
                                                    <p class="timeline-text">{{ $reservation->formatted_date_time }}</p>
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Customer Information -->
                        <div class="col-xl-12 mt-2">
                            <div class="card custom-card shadow-none border">
                                <div class="card-header">
                                    <div class="card-title">Customer Information</div>
                                </div>
                                <div class="card-body">
                                    <div class="row gy-3">
                                        <div class="col-md-4">
                                            <small class="text-muted">Customer Name</small>
                                            <div class="fw-semibold">{{ $reservation->customer_display_name }}</div>
                                        </div>
                                        <div class="col-md-4">
                                            <small class="text-muted">Email</small>
                                            <div class="fw-semibold">{{ $reservation->customer_email ?: 'Not provided' }}
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <small class="text-muted">Phone</small>
                                            <div class="fw-semibold">{{ $reservation->customer_phone ?: 'Not provided' }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="row">
                        <div class="col-xl-12">
                            <div class="d-flex gap-2 justify-content-start mt-4 pt-3 border-top">
                                @if ($reservation->canBeModified() && (auth()->user()->isAdmin() || $reservation->user_id === auth()->id()))
                                    <a href="{{ route('reservations.edit', $reservation) }}" class="btn btn-warning">
                                        <i class="ti ti-edit me-1"></i>Edit Reservation
                                    </a>
                                @endif
                                @if ($reservation->canBeCancelled() && (auth()->user()->isAdmin() || $reservation->user_id === auth()->id()))
                                    <button type="button" class="btn btn-danger"
                                        onclick="confirmCancelReservation({{ $reservation->id }}, '{{ $reservation->field->name }} - {{ $reservation->booking_date->format('M j, Y') }} {{ $reservation->time_range }}')">
                                        <i class="ti ti-x me-1"></i>Cancel Reservation
                                    </button>
                                @endif
                                @if ($reservation->canBeUncancelled() && (auth()->user()->isAdmin() || $reservation->user_id === auth()->id()))
                                    <button type="button" class="btn btn-primary"
                                        onclick="confirmRestoreReservation({{ $reservation->id }}, '{{ $reservation->field->name }} - {{ $reservation->booking_date->format('M j, Y') }} {{ $reservation->time_range }}')">
                                        <i class="bx bx-undo me-1"></i>Restore Reservation
                                    </button>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Restore Reservation Confirmation Modal -->
    <x-confirmation-modal
        modal-id="restoreReservationModal"
        type="success"
        icon="bx bx-undo"
        modal-title="Confirm Restore"
        title="Are you sure you want to restore the reservation for &quot;<span id='restoreReservationDetails' class='fw-semibold'></span>&quot;?"
        warning-text="This will reactivate the reservation and make it available again."
        cancel-text="No, Keep Cancelled"
        confirm-text="Yes, Restore Reservation"
        form-action="#"
        form-method="POST"
    />

    <!-- Cancel Reservation Confirmation Modal -->
    <x-confirmation-modal
        modal-id="cancelReservationModal"
        type="danger"
        icon="ri-alert-fill"
        modal-title="Confirm Cancel"
        title="Are you sure you want to cancel the reservation for &quot;<span id='cancelReservationDetails' class='fw-semibold'></span>&quot;?"
        warning-text="The reservation will be cancelled."
        cancel-text="No, Keep Reservation"
        confirm-text="Yes, Cancel Reservation"
        form-action="#"
        form-method="POST"
    />
@endsection

@push('scripts')
<script>
    // Restore reservation confirmation function
    function confirmRestoreReservation(reservationId, reservationDetails) {
        document.getElementById('restoreReservationDetails').textContent = reservationDetails;
        document.getElementById('restoreReservationModalForm').action = `/reservations/${reservationId}/uncancel`;
        const modal = new bootstrap.Modal(document.getElementById('restoreReservationModal'));
        modal.show();
    }

    // Cancel reservation confirmation function
    function confirmCancelReservation(reservationId, reservationDetails) {
        document.getElementById('cancelReservationDetails').textContent = reservationDetails;
        document.getElementById('cancelReservationModalForm').action = `/reservations/${reservationId}/cancel`;
        const modal = new bootstrap.Modal(document.getElementById('cancelReservationModal'));
        modal.show();
    }
</script>
@endpush

@push('styles')
    <style>
        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 10px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e9ecef;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }

        .timeline-marker {
            position: absolute;
            left: -25px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid #fff;
            box-shadow: 0 0 0 2px #e9ecef;
        }

        .timeline-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .timeline-text {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 0;
        }
    </style>
@endpush