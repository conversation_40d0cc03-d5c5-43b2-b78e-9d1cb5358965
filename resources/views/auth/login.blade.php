@extends('layouts.auth')

@section('title', 'Sign In - SMP Online')

@section('content')
    <div class="my-5 d-flex justify-content-center">
        <a href="{{ route('login') }}">
            <img src="{{ asset('assets/images/brand-logos/desktop-logo.png') }}" alt="logo" class="desktop-logo">
            <img src="{{ asset('assets/images/brand-logos/desktop-dark.png') }}" alt="logo" class="desktop-dark">
        </a>
    </div>
    <div class="card custom-card">
        <div class="card-body p-5">
            <p class="h5 fw-semibold mb-2 text-center">Sign In</p>
            <p class="mb-4 text-muted op-7 fw-normal text-center">Welcome! Please sign in to continue.</p>

            <!-- Session Status -->
            @if (session('status'))
                <div class="alert alert-success" role="alert">
                    {{ session('status') }}
                </div>
            @endif

            <form method="POST" action="{{ route('login') }}">
                @csrf
                <div class="row gy-3">
                    <div class="col-xl-12">
                        <label for="signin-username" class="form-label text-default">Email or Username</label>
                        <input type="text" class="form-control form-control-lg @error('login') is-invalid @enderror"
                            id="signin-username" name="login" value="{{ old('login') }}"
                            placeholder="Enter your email or username" required autofocus autocomplete="username">
                        @error('login')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                    <div class="col-xl-12 mb-2">
                        <label for="signin-password" class="form-label text-default d-block">Password
                            @if (Route::has('password.request'))
                                <a href="{{ route('password.request') }}" class="float-end text-danger">Forget password
                                    ?</a>
                            @endif
                        </label>
                        <div class="input-group">
                            <input type="password"
                                class="form-control form-control-lg @error('password') is-invalid @enderror"
                                id="signin-password" name="password" placeholder="Enter your password" required
                                autocomplete="current-password">
                            <button class="btn btn-light" type="button" onclick="createpassword('signin-password',this)"
                                id="button-addon2">
                                <i class="ri-eye-off-line align-middle"></i>
                            </button>
                            @error('password')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>
                        <div class="mt-2">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="" id="defaultCheck1"
                                    name="remember">
                                <label class="form-check-label text-muted fw-normal" for="defaultCheck1">
                                    Remember password ?
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-12 d-grid mt-3">
                        <button type="submit" class="btn btn-lg btn-primary">Sign In</button>
                    </div>
                </div>
            </form>
            <!-------------------------------------------------------------------------------------------------------------->
            <!--<div class="text-center">
                                    <p class="fs-12 text-muted mt-3">Don't have an account? <a href="{{ route('register') }}"
                                            class="text-primary">Sign Up</a></p>
                                </div> -->
            <!--<div class="text-center my-3 authentication-barrier">
                                    <span>OR</span>
                                </div>-->
            {{-- <div class="btn-list text-center">
                <p class="fs-12 text-muted">Demo Accounts:</p>
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-light btn-sm"
                        onclick="fillDemoCredentials('<EMAIL>', 'password')">
                        <i class="ri-shield-user-line me-2"></i>Admin Demo
                    </button>
                    <button type="button" class="btn btn-light btn-sm"
                        onclick="fillDemoCredentials('<EMAIL>', 'password')">
                        <i class="ri-user-line me-2"></i>Member Demo
                    </button>
                     <button type="button" class="btn btn-light btn-sm"
                        onclick="fillDemoCredentials('<EMAIL>', 'password')">
                        <i class="ri-customer-service-line me-2"></i>Client Demo
                    </button>
                </div>
            </div> --}}
        </div>
    </div>

    <script>
        function fillDemoCredentials(email, password) {
            document.getElementById('signin-username').value = email;
            document.getElementById('signin-password').value = password;
        }

        function createpassword(id, button) {
            var input = document.getElementById(id);
            var icon = button.querySelector('i');

            if (input.type === 'password') {
                input.type = 'text';
                icon.className = 'ri-eye-line align-middle';
            } else {
                input.type = 'password';
                icon.className = 'ri-eye-off-line align-middle';
            }
        }
    </script>
@endsection
